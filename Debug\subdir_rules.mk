################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
build-1548528193: ../empty.syscfg
	@echo 'Building file: "$<"'
	@echo 'Invoking: SysConfig'
	"D:/ti/ccstheia151/ccs/utils/sysconfig_1.21.1/sysconfig_cli.bat" --script "C:/Users/<USER>/workspace_ccstheia/TI_CAR1/empty.syscfg" -o "." -s "D:/ti/ccstheia/mspm0_sdk_2_05_00_05/.metadata/product.json" --compiler tic<PERSON>
	@echo 'Finished building: "$<"'
	@echo ' '

device_linker.cmd: build-1548528193 ../empty.syscfg
device.opt: build-1548528193
device.cmd.genlibs: build-1548528193
ti_msp_dl_config.c: build-1548528193
ti_msp_dl_config.h: build-1548528193
Event.dot: build-1548528193

%.o: ./%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1/Debug" -I"D:/ti/ccstheia/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include" -I"D:/ti/ccstheia/mspm0_sdk_2_05_00_05/source" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1/BSP/Inc" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1/APP/Inc" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1/DMP" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -gdwarf-3 -w -MMD -MP -MF"$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '

startup_mspm0g350x_ticlang.o: D:/ti/ccstheia/mspm0_sdk_2_05_00_05/source/ti/devices/msp/m0p/startup_system_files/ticlang/startup_mspm0g350x_ticlang.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1/Debug" -I"D:/ti/ccstheia/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include" -I"D:/ti/ccstheia/mspm0_sdk_2_05_00_05/source" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1/BSP/Inc" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1/APP/Inc" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1/DMP" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -gdwarf-3 -w -MMD -MP -MF"$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '

%.o: ../%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"D:/ti/ccstheia151/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1/Debug" -I"D:/ti/ccstheia/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include" -I"D:/ti/ccstheia/mspm0_sdk_2_05_00_05/source" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1/BSP/Inc" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1/APP/Inc" -I"C:/Users/<USER>/workspace_ccstheia/TI_CAR1/DMP" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -gdwarf-3 -w -MMD -MP -MF"$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '


