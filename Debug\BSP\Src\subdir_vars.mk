################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../BSP/Src/ADC.c \
../BSP/Src/Key_Led.c \
../BSP/Src/MPU6050.c \
../BSP/Src/Motor.c \
../BSP/Src/No_Mcu_Ganv_Grayscale_Sensor.c \
../BSP/Src/OLED.c \
../BSP/Src/OLED_Font.c \
../BSP/Src/PID.c \
../BSP/Src/PID_IQMath.c \
../BSP/Src/Serial.c \
../BSP/Src/SysTick.c \
../BSP/Src/Task.c \
../BSP/Src/Tracker.c 

C_DEPS += \
./BSP/Src/ADC.d \
./BSP/Src/Key_Led.d \
./BSP/Src/MPU6050.d \
./BSP/Src/Motor.d \
./BSP/Src/No_Mcu_Ganv_Grayscale_Sensor.d \
./BSP/Src/OLED.d \
./BSP/Src/OLED_Font.d \
./BSP/Src/PID.d \
./BSP/Src/PID_IQMath.d \
./BSP/Src/Serial.d \
./BSP/Src/SysTick.d \
./BSP/Src/Task.d \
./BSP/Src/Tracker.d 

OBJS += \
./BSP/Src/ADC.o \
./BSP/Src/Key_Led.o \
./BSP/Src/MPU6050.o \
./BSP/Src/Motor.o \
./BSP/Src/No_Mcu_Ganv_Grayscale_Sensor.o \
./BSP/Src/OLED.o \
./BSP/Src/OLED_Font.o \
./BSP/Src/PID.o \
./BSP/Src/PID_IQMath.o \
./BSP/Src/Serial.o \
./BSP/Src/SysTick.o \
./BSP/Src/Task.o \
./BSP/Src/Tracker.o 

OBJS__QUOTED += \
"BSP\Src\ADC.o" \
"BSP\Src\Key_Led.o" \
"BSP\Src\MPU6050.o" \
"BSP\Src\Motor.o" \
"BSP\Src\No_Mcu_Ganv_Grayscale_Sensor.o" \
"BSP\Src\OLED.o" \
"BSP\Src\OLED_Font.o" \
"BSP\Src\PID.o" \
"BSP\Src\PID_IQMath.o" \
"BSP\Src\Serial.o" \
"BSP\Src\SysTick.o" \
"BSP\Src\Task.o" \
"BSP\Src\Tracker.o" 

C_DEPS__QUOTED += \
"BSP\Src\ADC.d" \
"BSP\Src\Key_Led.d" \
"BSP\Src\MPU6050.d" \
"BSP\Src\Motor.d" \
"BSP\Src\No_Mcu_Ganv_Grayscale_Sensor.d" \
"BSP\Src\OLED.d" \
"BSP\Src\OLED_Font.d" \
"BSP\Src\PID.d" \
"BSP\Src\PID_IQMath.d" \
"BSP\Src\Serial.d" \
"BSP\Src\SysTick.d" \
"BSP\Src\Task.d" \
"BSP\Src\Tracker.d" 

C_SRCS__QUOTED += \
"../BSP/Src/ADC.c" \
"../BSP/Src/Key_Led.c" \
"../BSP/Src/MPU6050.c" \
"../BSP/Src/Motor.c" \
"../BSP/Src/No_Mcu_Ganv_Grayscale_Sensor.c" \
"../BSP/Src/OLED.c" \
"../BSP/Src/OLED_Font.c" \
"../BSP/Src/PID.c" \
"../BSP/Src/PID_IQMath.c" \
"../BSP/Src/Serial.c" \
"../BSP/Src/SysTick.c" \
"../BSP/Src/Task.c" \
"../BSP/Src/Tracker.c" 


